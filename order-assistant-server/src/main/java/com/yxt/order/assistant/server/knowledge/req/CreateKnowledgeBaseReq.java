package com.yxt.order.assistant.server.knowledge.req;

import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseSource;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建知识�?
 */
@Data
@ApiModel("创建知识库请求参�?)
public class CreateKnowledgeBaseReq {

  @ApiModelProperty("知识库名�?)
  @NotEmpty
  private String name;

  /**
   * 知识库描�?
   */
  private String description;


  @ApiModelProperty("知识库来�?)
  @NotNull
  private KnowledgeBaseSource source;

}

package org.springframework.cloud.netflix.ribbon;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.context.annotation.Configuration;

/**
 * 占位符类，用于解决第三方依赖中对已移除的 SpringClientFactory 的引用
 * 
 * 这是一个临时解决方案，用于兼容那些仍然引用已被移除的 Ribbon 相关类的第三方库。
 * 在 Spring Cloud 2020.x 及以后版本中，Netflix Ribbon 已被 Spring Cloud LoadBalancer 替代。
 */
@Configuration
@ConditionalOnMissingClass("org.springframework.cloud.netflix.ribbon.RibbonAutoConfiguration")
public class SpringClientFactory {
    
    // 这是一个空的占位符类，仅用于满足第三方依赖的类引用需求
    // 实际的负载均衡功能由 Spring Cloud LoadBalancer 提供
    
}
